<template>
  <div
    class="mt-17 mb-12 font-semibold text-[rgba(51,51,51,0.85)]"
    :class="{
      'text-15': !terminal,
      'text-[.32rem]': terminal,
    }"
  >
    基础信息
  </div>
  <div
    class="border-1 border-solid border-[#e6eef4]"
    :class="{
      'text-15/39': !terminal,
      'text-[.32rem]/39': terminal,
    }"
  >
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] rounded-tl-5 border-1 border-solid border-[#e6eef4]"
      >
        木仓昵称
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white rounded-tr-5 border-1 border-solid border-[#e6eef4] [border-left:0]"
      >
        {{ dashboard?.nickName }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        木仓ID
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-top:0] [border-left:0]"
        v-if="dashboard?.mucangId"
      >
        <div class="flex-1 whitespace-nowrap overflow-hidden text-ellipsis">
          {{ dashboard.mucangId }}
        </div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="copyMucangId(dashboard?.mucangId)"
          v-if="dashboard.mucangId"
        >
          复制
        </div>
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-top:0] [border-left:0]"
        v-else
      >
        <div
          class="flex flex-1 whitespace-nowrap overflow-hidden text-ellipsis"
        >
          <input
            type="text"
            v-model="phone"
            maxlength="11"
            placeholder="请输入手机号完成绑定"
          />
          <div
            class="flex-1 ml-10 cursor-pointer text-center"
            style="color: #04a5ff"
            @click="bindPhone"
          >
            绑定
          </div>
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        车型
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.carType && CarTypeMapper[dashboard.carType] }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        城市
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.cityName }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        手机号
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div>{{ dashboard?.phoneMask }}</div>
        <span
          class="w-60 cursor-pointer"
          style="color: #04a5ff"
          @click="showUserPhone"
          v-if="dashboard?.phoneMask"
          >显示</span
        >
        <span
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="onCallClick"
          v-if="dashboard?.phoneMask"
          >呼叫</span
        >
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        姓名
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">{{ dashboard?.name || "--" }}</div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        职业
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">{{ dashboard?.work || "--" }}</div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="editField('work', '职业')"
        >
          编辑
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        年龄
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">{{ dashboard?.age || "--" }}</div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="editField('age', '年龄')"
        >
          编辑
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        性别
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">
          {{
            dashboard?.gender !== undefined
              ? GenderMapper[dashboard.gender]
              : "--"
          }}
        </div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="editGender"
        >
          编辑
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        VIP类型
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.vipTypes }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        做题数
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.totalExercisesCount || "--" }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        模考次数
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.mockTimes || "--" }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        近五次模考平均分
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.latestFiveTimeAvgScore || "--" }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        跟进人
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div v-if="dashboard?.followUserName">
          {{ dashboard?.followUserName }}
        </div>
        <div v-else>
          <span>暂无</span
          ><span
            class="text-[#04a5ff] ml-10 cursor-pointer"
            @click="onFollowUserClick"
            >我来跟进</span
          >
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0] rounded-bl-5"
      >
        学车地图
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0] rounded-br-5"
      >
        <span
          class="text-[#04a5ff] cursor-pointer"
          @click="
            aMapPopUpCompRef?.open(
              session!.sessionKey,
              dashboard?.latitude,
              dashboard?.longitude,
              dashboard?.mucangId,
            )
          "
          >查看</span
        >
      </div>
    </div>
  </div>

  <!-- 客户标签 -->
  <div class="mt-16">
    <div
      class="mb-8 font-semibold text-[rgba(51,51,51,0.85)]"
      :class="{
        'text-15': !terminal,
        'text-[.32rem]': terminal,
      }"
    >
      客户标签
    </div>
    <div class="bg-white rounded-8 p-16">
      <CustomerTags v-if="dashboard?.leadNo" :lead-no="dashboard.leadNo" />
      <div v-else class="text-gray-400 text-14">暂无标签</div>
    </div>
  </div>

  <!-- 客户备注 -->
  <div class="mt-16">
    <div
      class="mb-8 font-semibold text-[rgba(51,51,51,0.85)]"
      :class="{
        'text-15': !terminal,
        'text-[.32rem]': terminal,
      }"
    >
      客户备注
    </div>
    <div class="bg-white rounded-8 p-16">
      <CustomerRemarks v-if="dashboard?.userNo" :user-no="dashboard.userNo" />
      <div v-else class="text-gray-400 text-14">暂无备注</div>
    </div>
  </div>
  <AMapPopUp ref="aMapPopUpCompRef"></AMapPopUp>
  <CallModel ref="callModelCompRef"></CallModel>
</template>

<script lang="ts" setup>
import { ref, inject, watch } from "vue";

import {
  MonaDashboard,
  queryUserPhone,
  salesFollowRecordBind,
  queryDashboard,
  updateUserInfo,
  bindPhoneStore,
} from "@/api/jxbm";
import { makeToast, prompt } from "@/utils/dom";
import { CarTypeMapper } from "@/utils/helpers";
import CustomerRemarks from "./CustomerRemarks.vue";
import CustomerTags from "./CustomerTags.vue";
import AMapPopUp from "@/components/Common/AMapPopUp/index.vue";
import CallModel from "@/components/ChatQuick/Common/CallModel.vue";
import { initWxWork } from "@/initialize/wwinit";
import { isWxwork } from "@/utils/platform";

const props = defineProps<{
  session?: {
    sessionKey: string;
  };
}>();
const emit = defineEmits<{
  (e: "update:modelValue", dashboard: MonaDashboard): void;
}>();

const dashboard = ref<MonaDashboard>();
const aMapPopUpCompRef = ref<InstanceType<typeof AMapPopUp>>();
const callModelCompRef = ref<InstanceType<typeof CallModel>>();
const terminal = inject("terminal") as 0 | 1;
const phone = ref();
const GenderMapper: Record<number, string> = {
  0: "女",
  1: "男",
  99: "未知",
};

const bindPhone = async () => {
  const res = await bindPhoneStore({ phone: phone.value });
  if (res.value) {
    makeToast("绑定成功");
    fetchDashBoardInfo();
  } else {
    makeToast("绑定失败");
  }
};

const copyMucangId = (mucangId: string) => {
  try {
    navigator.clipboard.writeText(mucangId);
    makeToast("复制成功");
  } catch {
    makeToast("复制失败");
  }
};

const showUserPhone = async () => {
  const { value: phone } = await queryUserPhone({
    mucangId: dashboard.value!.mucangId,
  });
  prompt({
    title: "用户手机号",
    content: `<div style="font-size: 20px;">${phone}</div>`,
    terminal,
    showCancel: false,
  });
};

/**
 * 销售人跟进
 */
const onFollowUserClick = async () => {
  if (dashboard.value?.mucangId) {
    const { value } = await salesFollowRecordBind({
      leadNo: dashboard.value?.leadNo,
    });
    if (value) {
      makeToast("绑定成功");
    } else {
      makeToast("绑定失败");
    }
  }
};

/**
 * 呼叫用户
 */
const onCallClick = async () => {
  if (dashboard.value) {
    // 调用呼叫组件，传递完整的参数
    callModelCompRef.value?.open(
      props.session!.sessionKey, // 会话密钥
      dashboard.value.mucangId, // 用户ID
      dashboard.value.cityCode || "", // 城市编码，如果没有则使用空字符串
    );
  }
};

/**
 * 编辑字段
 */
const editField = async (field: "name" | "work" | "age", fieldName: string) => {
  if (!props.session?.sessionKey) return;

  const currentValue = dashboard.value?.[field] || "";
  const inputType = field === "age" ? "number" : "text";

  await prompt({
    title: `编辑${fieldName}`,
    content: `<input type="${inputType}" id="editInput" value="${currentValue}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" placeholder="请输入${fieldName}">`,
    terminal,
    showCancel: true,
  });

  const input = document.getElementById("editInput") as HTMLInputElement;
  const newValue = input?.value?.trim();

  if (newValue && newValue !== String(currentValue)) {
    try {
      const updateData: any = { sessionKey: props.session.sessionKey };
      if (field === "age") {
        updateData[field] = parseInt(newValue);
      } else {
        updateData[field] = newValue;
      }

      const { value } = await updateUserInfo(updateData);
      if (value) {
        makeToast(`${fieldName}更新成功`);
        fetchDashBoardInfo();
      } else {
        makeToast(`${fieldName}更新失败`);
      }
    } catch (error) {
      makeToast(`${fieldName}更新失败`);
    }
  }
};

/**
 * 编辑性别
 */
const editGender = async () => {
  if (!props.session?.sessionKey) return;

  const currentGender = dashboard.value?.gender;
  const genderOptions = Object.entries(GenderMapper).map(([value, label]) => ({
    value: Number(value),
    label,
  }));

  const optionsHtml = genderOptions
    .map(
      (option) =>
        `<label style="
          display: flex;
          align-items: center;
          margin: 12px 0;
          padding: 10px 16px;
          border-radius: 8px;
          cursor: pointer;
          transition: background-color 0.2s;
          border: 1px solid #e6e6e6;
        " onmouseover="this.style.backgroundColor='#f8f9fa'; this.style.borderColor='#d0d7de';" onmouseout="this.style.backgroundColor='transparent'; this.style.borderColor='#e6e6e6';">
          <input type="radio" name="gender" value="${option.value}" ${currentGender === option.value ? "checked" : ""} style="
            margin: 0 12px 0 0;
            transform: scale(1.3);
            accent-color: #04a5ff;
            width: 50%;
          ">
          <span style="font-size: 16px; color: #333; font-weight: 500;">${option.label}</span>
    </label>`,
    )
    .join("");

  const dom = await prompt({
    title: "编辑性别",
    content: `<div style="padding: 12px 0; min-width: 200px;">${optionsHtml}</div>`,
    terminal,
    showCancel: true,
  });

  const selectedRadio = dom.querySelector(
    'input[name="gender"]:checked',
  ) as HTMLInputElement;

  if (selectedRadio) {
    const newGender = parseInt(selectedRadio.value);

    if (newGender !== currentGender) {
      try {
        const { value } = await updateUserInfo({
          sessionKey: props.session.sessionKey,
          gender: newGender,
        });
        if (value) {
          makeToast("性别更新成功");
          fetchDashBoardInfo();
        } else {
          makeToast("性别更新失败");
        }
      } catch (error) {
        makeToast("性别更新失败");
      }
    }
  } else {
    makeToast("请选择性别");
  }
};

const fetchDashBoardInfo = async () => {
  if (isWxwork) {
    await initWxWork();
  }
  dashboard.value = await queryDashboard({
    sessionKey: props.session!.sessionKey,
  });
  // 需要同步v-model
  emit("update:modelValue", dashboard.value);
};

watch(
  () => props.session,
  () => {
    fetchDashBoardInfo();
  },
  { immediate: true },
);
</script>
